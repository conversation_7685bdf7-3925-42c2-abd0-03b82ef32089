// GameSettings.cs
using UnityEngine;
using UnityEngine.InputSystem; // Input System
using Unity.Cinemachine;       // Cinemachine 3

[CreateAssetMenu(fileName = "GameSettings", menuName = "Config/Game Settings")]
public class GameSettings : ScriptableObject
{
    [Header("Look")]
    [Range(0.05f, 5f)] public float lookXSens = 1f;
    [Range(0.05f, 5f)] public float lookYSens = 1f;
    public bool invertY = false;

    [Header("FOV (Cinemachine Camera Lens)")]
    [Range(30f, 100f)] public float fovTopDown = 55f;
    [Range(30f, 100f)] public float fovTPS = 60f;

    [Header("Input Actions (optional)")]
    [Tooltip("Asset d'actions utilisé par PlayerInput. Laisse vide si tu le gères ailleurs.")]
    public InputActionAsset inputActions;

    [Tooltip("Clé PlayerPrefs pour les overrides de bindings.")]
    public string rebindsKey = "inputRebinds";

    [Header("Cinemachine Axis (optionnel)")]
    [Tooltip("Gain global appliqué sur les axes de caméra (CinemachineInputAxisController).")]
    [Range(0.1f, 10f)] public float cmLookGain = 1f;
    [Tooltip("Temps d'accélération de l'axe caméra.")]
    [Range(0f, 1f)] public float cmAccelTime = 0.05f;
    [Tooltip("Temps de décélération de l'axe caméra.")]
    [Range(0f, 1f)] public float cmDecelTime = 0.1f;

    // ---------- Persistence ----------
    public void Load()
    {
        lookXSens = PlayerPrefs.GetFloat("lookXSens", lookXSens);
        lookYSens = PlayerPrefs.GetFloat("lookYSens", lookYSens);
        invertY = PlayerPrefs.GetInt("invertY", invertY ? 1 : 0) == 1;
        fovTopDown = PlayerPrefs.GetFloat("fovTopDown", fovTopDown);
        fovTPS = PlayerPrefs.GetFloat("fovTPS", fovTPS);

        cmLookGain = PlayerPrefs.GetFloat("cmLookGain", cmLookGain);
        cmAccelTime = PlayerPrefs.GetFloat("cmAccelTime", cmAccelTime);
        cmDecelTime = PlayerPrefs.GetFloat("cmDecelTime", cmDecelTime);

        // Charge les rebinds (Input System)
        if (inputActions != null && PlayerPrefs.HasKey(rebindsKey))
        {
            string json = PlayerPrefs.GetString(rebindsKey);
            if (!string.IsNullOrEmpty(json))
                inputActions.LoadBindingOverridesFromJson(json); // doc officielle
        }
    }

    public void Save()
    {
        PlayerPrefs.SetFloat("lookXSens", lookXSens);
        PlayerPrefs.SetFloat("lookYSens", lookYSens);
        PlayerPrefs.SetInt("invertY", invertY ? 1 : 0);
        PlayerPrefs.SetFloat("fovTopDown", fovTopDown);
        PlayerPrefs.SetFloat("fovTPS", fovTPS);

        PlayerPrefs.SetFloat("cmLookGain", cmLookGain);
        PlayerPrefs.SetFloat("cmAccelTime", cmAccelTime);
        PlayerPrefs.SetFloat("cmDecelTime", cmDecelTime);

        // Sauve les rebinds (Input System)
        if (inputActions != null)
        {
            string json = inputActions.SaveBindingOverridesAsJson(); // doc officielle
            PlayerPrefs.SetString(rebindsKey, json);
        }

        PlayerPrefs.Save();
    }

    // ---------- Helpers pour le gameplay ----------
    /// <summary>
    /// Applique l'échelle de sensibilité / inversion Y au vecteur Look lu dans l'Input System.
    /// Usage: var look = settings.ApplyLookSensitivity(rawLook);
    /// </summary>
    public Vector2 ApplyLookSensitivity(Vector2 rawLook)
    {
        float y = invertY ? -rawLook.y : rawLook.y;
        return new Vector2(rawLook.x * lookXSens, y * lookYSens);
    }

    /// <summary>
    /// Applique le FOV sur une CinemachineCamera selon le mode.
    /// </summary>
    public void ApplyFov(CinemachineCamera vcam, bool isTopDown)
    {
        if (vcam == null) return;
        var lens = vcam.Lens; // CinemachineCamera.Lens (Field Of View)
        lens.FieldOfView = isTopDown ? fovTopDown : fovTPS;
        vcam.Lens = lens; // ré-assigne (struct)
    }

    /// <summary>
    /// Applique gain/accel/decel aux axes détectés par CinemachineInputAxisController
    /// (si tu l'ajoutes sur tes vCams).
    /// </summary>
    public void ApplyCinemachineAxisTuning(CinemachineInputAxisController axisController)
    {
        if (axisController == null) return;

        // Parcourt les axes découverts et règle gain/accel/decel.
        // L'API expose 'Controllers' via la classe de base (dynamique).
        foreach (var ctrl in axisController.Controllers)
        {
            ctrl.Gain = cmLookGain;
            ctrl.AccelTime = cmAccelTime;
            ctrl.DecelTime = cmDecelTime;
        }
    }

    /// <summary>
    /// Sauve les rebinds de l'Input System explicitement (si tu ne veux pas passer par Save()).
    /// </summary>
    public void SaveRebinds()
    {
        if (inputActions == null) return;
        string json = inputActions.SaveBindingOverridesAsJson();
        PlayerPrefs.SetString(rebindsKey, json);
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Charge les rebinds de l'Input System explicitement (si tu ne veux pas passer par Load()).
    /// </summary>
    public void LoadRebinds()
    {
        if (inputActions == null) return;
        if (PlayerPrefs.HasKey(rebindsKey))
        {
            string json = PlayerPrefs.GetString(rebindsKey);
            if (!string.IsNullOrEmpty(json))
                inputActions.LoadBindingOverridesFromJson(json);
        }
    }
}
