// GameSettings.cs
using UnityEngine;
using UnityEngine.InputSystem; // Input System
using Unity.Cinemachine;       // Cinemachine 3

[CreateAssetMenu(fileName = "GameSettings", menuName = "Config/Game Settings")]
public class GameSettings : ScriptableObject
{

    [Header("Bindings")]
    [Tooltip("Action reference pour SwitchView (pour changer la vue mode).")]
    public InputActionReference switchViewAction;
    public string switchViewBindingKey = "binding_SwitchView";

    [Header("Look")]
    public InputActionReference lookAction;         // Player/Look (Vector2)
    public InputActionReference shoulderSwapAction; // Player/ShoulderSwap (Button)
    [Range(0.05f, 5f)] public float lookXSens = 1f;
    [Range(0.05f, 5f)] public float lookYSens = 1f;
    public bool invertY = false;

    [Header("Look sensitivity")]
    [Range(0.01f, 0.5f)] public float mouseYawDegPerPixel = 0.12f;
    [Range(0.01f, 0.5f)] public float mousePitchDegPerPixel = 0.12f;
    [Range(30f, 360f)] public float stickYawDegPerSec = 180f;
    [Range(30f, 360f)] public float stickPitchDegPerSec = 150f;

    [Header("FOV (Cinemachine Camera Lens)")]
    [Range(30f, 100f)] public float fovTopDown = 55f;
    [Range(30f, 100f)] public float fovTPS = 60f;

    [Header("Input Actions (optional)")]
    [Tooltip("Asset d'actions utilisé par PlayerInput. Laisse vide si tu le gères ailleurs.")]
    public InputActionAsset inputActions;

    [Tooltip("Clé PlayerPrefs pour les overrides de bindings.")]
    public string rebindsKey = "inputRebinds";

    [Header("Cinemachine Axis (optionnel)")]
    [Tooltip("Gain global appliqué sur les axes de caméra (CinemachineInputAxisController).")]
    [Range(0.1f, 10f)] public float cmLookGain = 1f;
    [Tooltip("Temps d'accélération de l'axe caméra.")]
    [Range(0f, 1f)] public float cmAccelTime = 0.05f;
    [Tooltip("Temps de décélération de l'axe caméra.")]
    [Range(0f, 1f)] public float cmDecelTime = 0.1f;

    // ---------- Persistence ----------
    public void Load()
    {
        lookXSens = PlayerPrefs.GetFloat("lookXSens", lookXSens);
        lookYSens = PlayerPrefs.GetFloat("lookYSens", lookYSens);
        invertY = PlayerPrefs.GetInt("invertY", invertY ? 1 : 0) == 1;
        fovTopDown = PlayerPrefs.GetFloat("fovTopDown", fovTopDown);
        fovTPS = PlayerPrefs.GetFloat("fovTPS", fovTPS);

        cmLookGain = PlayerPrefs.GetFloat("cmLookGain", cmLookGain);
        cmAccelTime = PlayerPrefs.GetFloat("cmAccelTime", cmAccelTime);
        cmDecelTime = PlayerPrefs.GetFloat("cmDecelTime", cmDecelTime);

        // Charge les rebinds (Input System)
        if (inputActions != null && PlayerPrefs.HasKey(rebindsKey))
        {
            string json = PlayerPrefs.GetString(rebindsKey);
            if (!string.IsNullOrEmpty(json))
                inputActions.LoadBindingOverridesFromJson(json);
        }
    }

    public void Save()
    {
        PlayerPrefs.SetFloat("lookXSens", lookXSens);
        PlayerPrefs.SetFloat("lookYSens", lookYSens);
        PlayerPrefs.SetInt("invertY", invertY ? 1 : 0);
        PlayerPrefs.SetFloat("fovTopDown", fovTopDown);
        PlayerPrefs.SetFloat("fovTPS", fovTPS);

        PlayerPrefs.SetFloat("cmLookGain", cmLookGain);
        PlayerPrefs.SetFloat("cmAccelTime", cmAccelTime);
        PlayerPrefs.SetFloat("cmDecelTime", cmDecelTime);

        // Sauve les rebinds (Input System)
        if (inputActions != null)
        {
            string json = inputActions.SaveBindingOverridesAsJson(); // doc officielle
            PlayerPrefs.SetString(rebindsKey, json);
        }

        PlayerPrefs.Save();
    }

    // ---------- Helpers pour le gameplay ----------
    /// <summary>
    /// Applique l'échelle de sensibilité / inversion Y au vecteur Look lu dans l'Input System.
    /// Usage: var look = settings.ApplyLookSensitivity(rawLook);
    /// </summary>
    public Vector2 ApplyLookSensitivity(Vector2 rawLook)
    {
        float y = invertY ? -rawLook.y : rawLook.y;
        return new Vector2(rawLook.x * lookXSens, y * lookYSens);
    }

    /// <summary>
    /// Applique le FOV sur une CinemachineCamera selon le mode.
    /// </summary>
    public void ApplyFov(CinemachineCamera vcam, bool isTopDown)
    {
        if (vcam == null) return;
        var lens = vcam.Lens; // CinemachineCamera.Lens (Field Of View)
        lens.FieldOfView = isTopDown ? fovTopDown : fovTPS;
        vcam.Lens = lens; // ré-assigne (struct)
    }

    /// <summary>
    /// Applique gain/accel/decel aux axes détectés par CinemachineInputAxisController
    /// (si tu l'ajoutes sur tes vCams).
    /// </summary>
    public void ApplyCinemachineAxisTuning(Unity.Cinemachine.CinemachineInputAxisController axisController)
    {
        if (axisController == null) return;

        // Si tu ajoutes/retire des composants d’axe à la volée, tu peux resynchroniser une fois :
        // axisController.SynchronizeControllers();

        var controllers = axisController.Controllers; // liste dynamique des axes détectés
        foreach (var c in controllers)
        {
            // 1) Réglages de réactivité : passent par le Driver (DefaultInputAxisDriver)
            var d = c.Driver;                // struct => on modifie puis on réassigne
            d.AccelTime = cmAccelTime;      // temps d'accélération
            d.DecelTime = cmDecelTime;      // temps de décélération
            c.Driver = d;                // IMPORTANT : réécriture du struct

            // 2) Sensibilité / inversion : passent par Reader (Input)
            //    - Avec le nouveau Input System : 'Gain'
            //    - Avec l'input legacy : 'LegacyGain'
            // NB: Selon ta version de CM, 'Gain' fait partie du Reader (doc/manuel).
            try
            {
                // Nouveau Input System
                var gainField = c.Input.GetType().GetField("Gain");
                if (gainField != null) gainField.SetValue(c.Input, cmLookGain);

                // Legacy Input (fallback)
                var legacyGainField = c.Input.GetType().GetField("LegacyGain");
                if (legacyGainField != null) legacyGainField.SetValue(c.Input, cmLookGain);
            }
            catch { /* au cas où la réflexion est inutile/indispo, on ignore */ }
        }
    }


    /// <summary>
    /// Sauve les rebinds de l'Input System explicitement (si tu ne veux pas passer par Save()).
    /// </summary>
    public void SaveRebinds()
    {
        if (inputActions == null) return;
        string json = inputActions.SaveBindingOverridesAsJson();
        PlayerPrefs.SetString(rebindsKey, json);
        PlayerPrefs.Save();
    }

    /// <summary>
    /// Charge les rebinds de l'Input System explicitement (si tu ne veux pas passer par Load()).
    /// </summary>
    public void LoadRebinds()
    {
        if (inputActions == null) return;
        if (PlayerPrefs.HasKey(rebindsKey))
        {
            string json = PlayerPrefs.GetString(rebindsKey);
            if (!string.IsNullOrEmpty(json))
                inputActions.LoadBindingOverridesFromJson(json);
        }
    }

    /// <summary>
    /// Applique les rebinds chargés depuis PlayerPrefs à un asset d'actions en cours d'exécution.
    /// </summary>
    public void ApplyRebindsTo(InputActionAsset runtimeAsset)
    {
        if (runtimeAsset == null) return;
        if (!PlayerPrefs.HasKey(rebindsKey)) return;

        var json = PlayerPrefs.GetString(rebindsKey);
        if (!string.IsNullOrEmpty(json))
            runtimeAsset.LoadBindingOverridesFromJson(json);
    }
}
