using UnityEngine;
using UnityEngine.InputSystem;
using Unity.Cinemachine;
using Game;

public class CameraTargetOrbit : MonoBehaviour
{
    [Header("Refs")]
    public GameSettings Settings;
    public PlayerController player;
    public CinemachineCamera vcamTPS;

    [Header("Actions (map/action)")]
    public string lookPath = "Player/Look";
    public string shoulderSwapPath = "Player/ShoulderSwap"; // optionnel

    [Header("Yaw/Pitch")]
    public float yawSpeed = 160f;    // °/s
    public float pitchSpeed = 120f;  // °/s
    public float pitchMin = -50f;
    public float pitchMax = 70f;

    float yaw, pitch;
    InputAction look, shoulderSwap;
    float lastBindAttempt;
    CinemachineThirdPersonFollow tpf;

    void Awake()
    {
        if (vcamTPS) tpf = vcamTPS.GetComponent<CinemachineThirdPersonFollow>();
        var e = transform.rotation.eulerAngles;
        yaw = e.y; pitch = ClampPitch(e.x);
    }

    void OnEnable()
    {
        BindFromSettings();
        EnableInputs(true);
    }
    void OnDisable() => EnableInputs(false);

    void Update()
    {
        if (!player || player.Mode != ViewMode.TPS) return;

        if (look == null)
        {
            if (Time.time - lastBindAttempt > 1f)
            {
                TryBind();
                lastBindAttempt = Time.time;
            }

            if (look == null)
            {
                if (Time.frameCount % 60 == 0)
                {
                    Debug.LogWarning($"[CameraTargetOrbit] Look action still null. SettingsManager: {(SettingsManager.Instance != null ? "Found" : "NULL")}");
                }
                return;
            }
        }

        // shoulder swap
        if (shoulderSwap != null && shoulderSwap.triggered && tpf != null)
        {
            var off = tpf.ShoulderOffset;
            off.x = -off.x;
            tpf.ShoulderOffset = off;
        }

        // lire le delta "Look" (doit être bindé à <Mouse>/delta et/ou <Gamepad>/rightStick)
        Vector2 v = look.ReadValue<Vector2>();
        var src = look.activeControl?.device;

        // -- Souris: degrés par pixel (PAS de deltaTime) --
        if (src is Mouse)
        {
            float sx = Settings ? Settings.lookXSens : 0.12f; // pense en °/pixel
            float sy = Settings ? (Settings.invertY ? -Settings.lookYSens : Settings.lookYSens) : 0.12f;

            yaw += v.x * sx;
            pitch -= v.y * sy;
        }
        // -- Manette: degrés par seconde (AVEC deltaTime) --
        else
        {
            const float yawRateDegPerSec = 160f;
            const float pitchRateDegPerSec = 120f;

            yaw += v.x * yawRateDegPerSec * Time.deltaTime;
            pitch -= v.y * pitchRateDegPerSec * Time.deltaTime;
        }

        pitch = Mathf.Clamp(pitch, pitchMin, pitchMax);
        transform.rotation = Quaternion.Euler(pitch, yaw, 0f);
    }

    void TryBind()
    {
        var mgr = SettingsManager.Instance;
        if (mgr != null)
        {
            // Try different variations of the action path
            look = mgr.GetAction(lookPath) ?? mgr.GetAction("Look") ?? mgr.GetAction("Player.Look");
            shoulderSwap = mgr.GetAction(shoulderSwapPath) ?? mgr.GetAction("ShoulderSwap");

            if (look != null)
            {
                Debug.Log($"[CameraTargetOrbit] Successfully bound Look action: {look.name}");
            }
            else
            {
                Debug.LogWarning($"[CameraTargetOrbit] Look action not found. Tried paths: {lookPath}, Look, Player.Look");

                // Create fallback action as last resort
                Debug.LogWarning("[CameraTargetOrbit] Creating fallback Look action with mouse delta binding");
                look = new InputAction("Look_Fallback", InputActionType.Value, "<Mouse>/delta");
                look.Enable();
            }
        }
        else
        {
            Debug.LogWarning("[CameraTargetOrbit] SettingsManager.Instance is null - cannot bind input actions");
        }

        EnableInputs(true);
    }

    void EnableInputs(bool enable)
    {
        if (enable) { look?.Enable(); shoulderSwap?.Enable(); }
        else { look?.Disable(); shoulderSwap?.Disable(); }
    }

    float ClampPitch(float x)
    {
        // remet un angle 0..360 en -180..180 avant clamp min/max
        x = (x > 180f) ? x - 360f : x;
        return Mathf.Clamp(x, pitchMin, pitchMax);
    }

    public void ResetToPlayerYaw(float pitchDeg)
    {
        if (!player) return;
        yaw = player.transform.eulerAngles.y;
        pitch = Mathf.Clamp(pitchDeg, pitchMin, pitchMax);
        transform.rotation = Quaternion.Euler(pitch, yaw, 0f);
    }

    void BindFromSettings()
    {
        look = Settings?.lookAction?.action
            ?? Settings?.inputActions?.FindAction("Player/Look", false);
        shoulderSwap = Settings?.shoulderSwapAction?.action
            ?? Settings?.inputActions?.FindAction("Player/ShoulderSwap", false);
    }
}
