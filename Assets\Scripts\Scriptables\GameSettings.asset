%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!114 &11400000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 0}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: ec93fdc737b123249bee869805990344, type: 3}
  m_Name: GameSettings
  m_EditorClassIdentifier: Assembly-CSharp::GameSettings
  switchViewAction: {fileID: -8293427534131401305, guid: 35845fe01580c41289b024647b1d1c53, type: 3}
  switchViewBindingKey: SwitchView
  lookAction: {fileID: -5630151704836100654, guid: 35845fe01580c41289b024647b1d1c53, type: 3}
  shoulderSwapAction: {fileID: -6010659315128997574, guid: 35845fe01580c41289b024647b1d1c53, type: 3}
  lookXSens: 0.5
  lookYSens: 0.5
  invertY: 0
  mouseYawDegPerPixel: 0.12
  mousePitchDegPerPixel: 0.12
  stickYawDegPerSec: 180
  stickPitchDegPerSec: 150
  fovTopDown: 55
  fovTPS: 60
  inputActions: {fileID: -944628639613478452, guid: 35845fe01580c41289b024647b1d1c53, type: 3}
  rebindsKey: inputRebinds
  cmLookGain: 1
  cmAccelTime: 0.05
  cmDecelTime: 0.1
