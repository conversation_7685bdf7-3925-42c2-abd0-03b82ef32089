fileFormatVersion: 2
guid: 773fa55be186bfc46910c191c5c57d8f
timeCreated: **********
licenseType: Store
ModelImporter:
  serializedVersion: 19
  fileIDToRecycleName:
    100000: SM_Veh_Car_Sports_01
    100002: //RootNode
    100004: SM_Veh_Sports_Glass
    100006: SM_Veh_Sports_SteeringW
    100008: SM_Veh_Sports_Wheel_fl
    100010: SM_Veh_Sports_Wheel_fr
    100012: SM_Veh_Sports_Wheel_rl
    100014: SM_Veh_Sports_Wheel_rr
    400000: SM_Veh_Car_Sports_01
    400002: //RootNode
    400004: SM_Veh_Sports_Glass
    400006: SM_Veh_Sports_SteeringW
    400008: SM_Veh_Sports_Wheel_fl
    400010: SM_Veh_Sports_Wheel_fr
    400012: SM_Veh_Sports_Wheel_rl
    400014: SM_Veh_Sports_Wheel_rr
    2300000: SM_Veh_Car_Sports_01
    2300002: SM_Veh_Sports_Glass
    2300004: SM_Veh_Sports_SteeringW
    2300006: SM_Veh_Sports_Wheel_fl
    2300008: SM_Veh_Sports_Wheel_fr
    2300010: SM_Veh_Sports_Wheel_rl
    2300012: SM_Veh_Sports_Wheel_rr
    2300014: //RootNode
    3300000: SM_Veh_Car_Sports_01
    3300002: SM_Veh_Sports_Glass
    3300004: SM_Veh_Sports_SteeringW
    3300006: SM_Veh_Sports_Wheel_fl
    3300008: SM_Veh_Sports_Wheel_fr
    3300010: SM_Veh_Sports_Wheel_rl
    3300012: SM_Veh_Sports_Wheel_rr
    3300014: //RootNode
    4300000: SM_Veh_Car_Sports_01
    4300002: SM_Veh_Sports_Wheel_rl
    4300004: SM_Veh_Sports_SteeringW
    4300006: SM_Veh_Sports_Wheel_fl
    4300008: SM_Veh_Sports_Wheel_fr
    4300010: SM_Veh_Sports_Wheel_rr
    4300012: SM_Veh_Sports_Glass
    9500000: //RootNode
  materials:
    importMaterials: 0
    materialName: 0
    materialSearch: 1
  animations:
    legacyGenerateAnimations: 4
    bakeSimulation: 0
    resampleCurves: 1
    optimizeGameObjects: 0
    motionNodeName: 
    rigImportErrors: 
    rigImportWarnings: 
    animationImportErrors: 
    animationImportWarnings: 
    animationRetargetingWarnings: 
    animationDoRetargetingWarnings: 0
    animationCompression: 1
    animationRotationError: 0.5
    animationPositionError: 0.5
    animationScaleError: 0.5
    animationWrapMode: 0
    extraExposedTransformPaths: []
    clipAnimations: []
    isReadable: 0
  meshes:
    lODScreenPercentages: []
    globalScale: 1
    meshCompression: 0
    addColliders: 0
    importBlendShapes: 0
    swapUVChannels: 0
    generateSecondaryUV: 0
    useFileUnits: 1
    optimizeMeshForGPU: 0
    keepQuads: 0
    weldVertices: 0
    secondaryUVAngleDistortion: 8
    secondaryUVAreaDistortion: 15.000001
    secondaryUVHardAngle: 88
    secondaryUVPackMargin: 4
    useFileScale: 1
  tangentSpace:
    normalSmoothAngle: 0
    normalImportMode: 1
    tangentImportMode: 3
  importAnimation: 0
  copyAvatar: 0
  humanDescription:
    serializedVersion: 2
    human: []
    skeleton: []
    armTwist: 0.5
    foreArmTwist: 0.5
    upperLegTwist: 0.5
    legTwist: 0.5
    armStretch: 0.05
    legStretch: 0.05
    feetSpacing: 0
    rootMotionBoneName: 
    rootMotionBoneRotation: {x: 0, y: 0, z: 0, w: 1}
    hasTranslationDoF: 0
    hasExtraRoot: 0
    skeletonHasParents: 1
  lastHumanDescriptionAvatarSource: {instanceID: 0}
  animationType: 0
  humanoidOversampling: 1
  additionalBone: 0
  userData: 
  assetBundleName: 
  assetBundleVariant: 
