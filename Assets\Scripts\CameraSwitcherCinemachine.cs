using UnityEngine;
using UnityEngine.InputSystem;
using Unity.Cinemachine;
using Game;

public class CameraSwitcherCinemachine : MonoBehaviour
{
    public CinemachineCamera TopDownCam;
    public CinemachineCamera TpsCam;
    public GameSettings Settings;

    public PlayerController player;


    public ViewMode Mode = ViewMode.TopDown;

    InputAction switchView;
    bool _bound;

    void Awake()
    {
        Settings?.Load();
        switchView = Settings?.switchViewAction?.action;
        ApplyMode(Mode, true);

        if (player is null)
        {
            Debug.LogWarning("[CameraSwitcher] PlayerController not found");
            return;
        }
        if (player != null)
        {
            player.SetMode(Mode);
        }

    }

    void OnEnable()
    {
        switchView?.Enable();
    }

    void OnDisable()
    {
        switchView?.Disable();
    }

    void Start()
    {
        TryBindAction();
        switchView?.Enable();
    }

    void Update()
    {
        if (switchView != null && switchView.triggered)
        {
            Mode = (Mode == ViewMode.TopDown) ? ViewMode.TPS : ViewMode.TopDown;
            ApplyMode(Mode, false);
        }

        // FOV live
        if (TopDownCam) { var l = TopDownCam.Lens; l.FieldOfView = Settings ? Settings.fovTopDown : 55f; TopDownCam.Lens = l; }
        if (TpsCam) { var l = TpsCam.Lens; l.FieldOfView = Settings ? Settings.fovTPS : 60f; TpsCam.Lens = l; }
    }


    void TryBindAction()
    {
        if (_bound) return;

        var mgr = SettingsManager.Instance;
        if (mgr != null)
        {
            var act = mgr.GetAction("SwitchView");
            if (act != null)
            {
                switchView = act;
                _bound = true;
                return;
            }
        }

        if (switchView == null)
        {
            switchView = new InputAction("SwitchView", InputActionType.Button);
            switchView.AddBinding("<Keyboard>/tab");
            switchView.Enable();
            _bound = true;
            Debug.LogWarning("[CameraSwitcher] Using local fallback binding <Keyboard>/tab");
        }
    }

    void ApplyMode(ViewMode m, bool instant)
    {
        if (!TopDownCam || !TpsCam) return;


        TpsCam.PreviousStateIsValid = false;
        TopDownCam.Priority = (m == ViewMode.TopDown) ? 20 : 0;
        TpsCam.Priority = (m == ViewMode.TPS) ? 20 : 0;

        player?.SetMode(m);

        if (m == ViewMode.TPS)
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
        else
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }

        if (instant)
        {
            var brain = Camera.main?.GetComponent<CinemachineBrain>();
            if (brain) brain.DefaultBlend.Time = 0f;
        }
    }
}
