using UnityEngine;
using UnityEngine.InputSystem;
using Unity.Cinemachine;

public class CameraSwitcherCinemachine : MonoBehaviour
{
    public CinemachineCamera TopDownCam;
    public CinemachineCamera TpsCam;
    public GameSettings Settings;

    public enum ViewMode { TopDown, TPS }
    public ViewMode Mode = ViewMode.TopDown;

    InputAction switchView;
    bool _bound;

    void Awake()
    {
        Settings?.Load();
        ApplyMode(Mode, true);
    }

    void OnEnable()
    {
        TryBindAction();
        switchView?.Enable();
    }

    void OnDisable()
    {
        switchView?.Disable();
    }

    void Start()
    {
        TryBindAction();
        switchView?.Enable();
    }

    void Update()
    {
        if (!_bound) { TryBindAction(); switchView?.Enable(); }

        bool pressed =
            (switchView != null && switchView.triggered) ||
            (Keyboard.current?.tabKey.wasPressedThisFrame ?? false);

        if (pressed)
        {
            Mode = (Mode == ViewMode.TopDown) ? ViewMode.TPS : ViewMode.TopDown;
            ApplyMode(Mode, false);
            Debug.Log("[CameraSwitcher] SwitchView triggered");
        }

        if (TopDownCam) { var l = TopDownCam.Lens; l.FieldOfView = Settings ? Settings.fovTopDown : 55f; TopDownCam.Lens = l; }
        if (TpsCam) { var l = TpsCam.Lens; l.FieldOfView = Settings ? Settings.fovTPS : 60f; TpsCam.Lens = l; }
    }

    void TryBindAction()
    {
        if (_bound) return;

        var mgr = SettingsManager.Instance;
        if (mgr != null)
        {
            var act = mgr.GetAction("SwitchView");
            if (act != null)
            {
                switchView = act;
                _bound = true;
                Debug.Log("[CameraSwitcher] Bound SwitchView from SettingsManager");
                return;
            }
        }

        if (switchView == null)
        {
            switchView = new InputAction("SwitchView", InputActionType.Button);
            switchView.AddBinding("<Keyboard>/tab");
            switchView.Enable();
            _bound = true;
            Debug.LogWarning("[CameraSwitcher] Using local fallback binding <Keyboard>/tab");
        }
    }

    void ApplyMode(ViewMode m, bool instant)
    {
        if (!TopDownCam || !TpsCam) return;

        TopDownCam.Priority = (m == ViewMode.TopDown) ? 20 : 0;
        TpsCam.Priority = (m == ViewMode.TPS) ? 20 : 0;

        if (m == ViewMode.TPS) { Cursor.lockState = CursorLockMode.Locked; Cursor.visible = false; }
        else { Cursor.lockState = CursorLockMode.None; Cursor.visible = true; }

        if (instant)
        {
            var brain = Camera.main?.GetComponent<CinemachineBrain>();
            if (brain) brain.DefaultBlend.Time = 0f;
        }
    }
}
