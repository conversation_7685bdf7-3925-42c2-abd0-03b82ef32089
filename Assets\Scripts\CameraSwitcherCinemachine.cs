// CameraSwitcherCinemachine.cs
using UnityEngine;
using UnityEngine.InputSystem;
using Unity.Cinemachine;

public class CameraSwitcherCinemachine : MonoBehaviour
{
    public CinemachineCamera TopDownCam;
    public CinemachineCamera TpsCam;
    public InputActionReference SwitchView; // "SwitchView" (Tab)
    public GameSettings Settings;

    public enum ViewMode { TopDown, TPS }
    public ViewMode Mode = ViewMode.TopDown;

    void Awake()
    {
        Settings?.Load();
        ApplyMode(Mode, true);
    }

    void OnEnable() { SwitchView?.action?.Enable(); }
    void OnDisable() { SwitchView?.action?.Disable(); }

    void Update()
    {
        if (SwitchView && SwitchView.action.WasPerformedThisFrame())
        {
            Mode = (Mode == ViewMode.TopDown) ? ViewMode.TPS : ViewMode.TopDown;
            ApplyMode(Mode, false);
        }

        // FOV live depuis settings
        if (TopDownCam) TopDownCam.Lens.FieldOfView = Settings ? Settings.fovTopDown : 55f;
        if (TpsCam) TpsCam.Lens.FieldOfView = Settings ? Settings.fovTPS : 60f;
    }

    void ApplyMode(ViewMode m, bool instant)
    {
        if (!TopDownCam || !TpsCam) return;

        TopDownCam.Priority = (m == ViewMode.TopDown) ? 20 : 0;
        TpsCam.Priority = (m == ViewMode.TPS) ? 20 : 0;

        if (m == ViewMode.TPS)
        {
            Cursor.lockState = CursorLockMode.Locked;
            Cursor.visible = false;
        }
        else
        {
            Cursor.lockState = CursorLockMode.None;
            Cursor.visible = true;
        }

        // Optionnel : blend instantané
        if (instant)
        {
            var brain = Camera.main?.GetComponent<CinemachineBrain>();
            if (brain) brain.DefaultBlend.Time = 0f;
        }
    }
}
