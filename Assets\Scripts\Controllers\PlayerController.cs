using UnityEngine;
using Game;
using UnityEngine.InputSystem;

[RequireComponent(typeof(CharacterController))]

public class PlayerController : MonoBehaviour
{
    [Header("Refs")]
    public CharacterController controller;
    public Transform cameraRoot;

    public CameraTargetOrbit cameraOrbit;

    [Header("Movement")]
    public float walkSpeed = 5f;
    public float sprintSpeed = 8f;
    public float rotationLerp = 12f;

    [Header("Jump/Gravity")]
    public float jumpHeight = 1.2f;
    public float gravity = -20f;

    [Header("Top-Down look at mouse")]
    public LayerMask groundMask = ~0;     // couches “sol” pour le Raycast
    public float mouseRayMaxDist = 500f;  // portée du rayon
    public bool fallbackToPlane = true;

    [Header("TPS options")]
    public bool tpsAlignToCameraYaw = true;
    public float bodyTurnSpeed = 540f;
    public float yawDeadZone = 8f;
    public bool allowFreeLook = true;    // Alt ne tourne pas le corps

    bool freelookHeld => SettingsManager.Instance?.GetAction("Player/FreeLook")?.IsPressed() ?? false;
    bool aiming => SettingsManager.Instance?.GetAction("Player/Aim")?.IsPressed() ?? false;

    public GameSettings Settings;

    Vector2 moveInput;
    bool jumpQueued;
    bool sprintHeld;

    ViewMode mode = ViewMode.TopDown;

    Vector3 velocity;

    void Reset()
    {
        controller = GetComponent<CharacterController>();
        if (Camera.main) cameraRoot = Camera.main.transform;
    }

    void Awake()
    {
        Settings?.Load();
    }

    void Update()
    {
        float dt = Time.deltaTime;

        // Inputs
        moveInput = SettingsManager.Instance?.GetAction("Move")?.ReadValue<Vector2>() ?? Vector2.zero;
        sprintHeld = SettingsManager.Instance?.GetAction("Sprint")?.IsPressed() ?? false;
        if (SettingsManager.Instance?.GetAction("Jump")?.WasPressedThisFrame() ?? false){}
            jumpQueued = true;

        // Cam de référence
        var cam = GetActiveCamera();
        if (!cameraRoot && cam) cameraRoot = cam.transform;

        // Direction de déplacement alignée caméra
        Vector3 desired = Vector3.zero;
        if (cameraRoot)
        {
            Vector3 fwd = cameraRoot.forward; fwd.y = 0f; fwd.Normalize();
            Vector3 right = cameraRoot.right; right.y = 0f; right.Normalize();
            desired = fwd * moveInput.y + right * moveInput.x;
        }
        else desired = new Vector3(moveInput.x, 0f, moveInput.y);
        desired = Vector3.ClampMagnitude(desired, 1f);

        float targetSpeed = sprintHeld ? sprintSpeed : walkSpeed;
        Vector3 planarVel = desired * targetSpeed;

        // Orientation
        if (mode == ViewMode.TopDown)
        {
            // -> Oriente vers la souris si possible, sinon fallback: direction de déplacement
            if (!TryGetMousePlanarDir(cam, out Vector3 faceDir))
                faceDir = (desired.sqrMagnitude > 1e-4f) ? desired : Vector3.zero;

            if (faceDir.sqrMagnitude > 1e-4f)
            {
                var targetRot = Quaternion.LookRotation(faceDir, Vector3.up);
                transform.rotation = Quaternion.Slerp(transform.rotation, targetRot, rotationLerp * dt);
            }
        }
        if (mode == ViewMode.TPS)
        {
            // direction de référence = yaw caméra
            if (cameraRoot)
            {
                Vector3 camYaw = cameraRoot.forward; camYaw.y = 0f; camYaw.Normalize();

                // freelook : ne pas tourner le corps si Alt est maintenu
                bool lockBody = allowFreeLook && freelookHeld;

                // rotation du corps uniquement si on vise, si on se déplace, ou si pas de freelook
                bool shouldAlign = aiming || desired.sqrMagnitude > 0.0001f || !lockBody;

                if (shouldAlign && camYaw.sqrMagnitude > 1e-4f)
                {
                    float targetYaw = Mathf.Atan2(camYaw.x, camYaw.z) * Mathf.Rad2Deg;
                    float currentYaw = transform.eulerAngles.y;
                    float delta = Mathf.DeltaAngle(currentYaw, targetYaw);

                    if (Mathf.Abs(delta) > yawDeadZone)
                    {
                        float maxStep = bodyTurnSpeed * Time.deltaTime;
                        float newYaw = Mathf.MoveTowardsAngle(currentYaw, targetYaw, maxStep);
                        transform.rotation = Quaternion.Euler(0f, newYaw, 0f);
                    }
                }
            }
        }

        // Saut / Gravité (isGrounded est l'état après le dernier Move)
        if (controller.isGrounded)
        {
            if (velocity.y < 0f) velocity.y = -2f;
            if (jumpQueued)
            {
                velocity.y = Mathf.Sqrt(-2f * gravity * jumpHeight);
                jumpQueued = false;
            }
        }
        velocity.y += gravity * dt;

        // Un seul Move par frame
        controller.Move((planarVel + Vector3.up * velocity.y) * dt);
    }


    public void SetMode(ViewMode m)
    {
        mode = m;
        if (cameraOrbit != null)
        {
            cameraOrbit.ResetToPlayerYaw(0f);
        }
    }

    public ViewMode Mode => mode;

    // ---- Helpers ----

    Camera GetActiveCamera()
    {
        // Si un CinemachineBrain est sur la Main Camera, sa OutputCamera est la cam “de sortie”
        var main = Camera.main;
        var brain = main ? main.GetComponent<Unity.Cinemachine.CinemachineBrain>() : null;
        return brain ? brain.OutputCamera : main;  // CM3: OutputCamera donne la Camera contrôlée par le Brain
    }

    bool TryGetMousePlanarDir(Camera cam, out Vector3 planarDir)
    {
        planarDir = Vector3.zero;
        if (!cam) return false;

        var mouse = Mouse.current;
        if (mouse == null) return false;

        // Sécurité : coordonnées écran valides (évite les frustums warnings)
        Vector2 p = mouse.position.ReadValue();                            // Input System mouse pos
        if (p.x < 0 || p.x >= Screen.width || p.y < 0 || p.y >= Screen.height) return false;

        Ray ray = cam.ScreenPointToRay(p);                                 // écran -> rayon monde
        Vector3 hitPoint;

        // 1) Raycast sur le sol
        if (Physics.Raycast(ray, out RaycastHit hit, mouseRayMaxDist, groundMask, QueryTriggerInteraction.Ignore))
        {
            hitPoint = hit.point;
        }
        // 2) Fallback: intersecter un plan horizontal à la hauteur du joueur
        else if (fallbackToPlane)
        {
            var plane = new Plane(Vector3.up, new Vector3(0f, transform.position.y, 0f));
            if (!plane.Raycast(ray, out float enter)) return false;
            hitPoint = ray.GetPoint(enter);
        }
        else return false;

        Vector3 to = hitPoint - transform.position;
        to.y = 0f;
        float mag2 = to.sqrMagnitude;
        if (mag2 < 1e-6f) return false;
        planarDir = to / Mathf.Sqrt(mag2);
        return true;
    }
}