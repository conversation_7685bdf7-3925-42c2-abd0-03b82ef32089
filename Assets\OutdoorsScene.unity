%YAML 1.1
%TAG !u! tag:unity3d.com,2011:
--- !u!29 &1
OcclusionCullingSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_OcclusionBakeSettings:
    smallestOccluder: 5
    smallestHole: 0.25
    backfaceThreshold: 100
  m_SceneGUID: 00000000000000000000000000000000
  m_OcclusionCullingData: {fileID: 0}
--- !u!104 &2
RenderSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 10
  m_Fog: 0
  m_FogColor: {r: 0.5, g: 0.5, b: 0.5, a: 1}
  m_FogMode: 3
  m_FogDensity: 0.01
  m_LinearFogStart: 0
  m_LinearFogEnd: 300
  m_AmbientSkyColor: {r: 0.212, g: 0.227, b: 0.259, a: 1}
  m_AmbientEquatorColor: {r: 0.114, g: 0.125, b: 0.133, a: 1}
  m_AmbientGroundColor: {r: 0.047, g: 0.043, b: 0.035, a: 1}
  m_AmbientIntensity: 1
  m_AmbientMode: 0
  m_SubtractiveShadowColor: {r: 0.42, g: 0.478, b: 0.627, a: 1}
  m_SkyboxMaterial: {fileID: 0}
  m_HaloStrength: 0.5
  m_FlareStrength: 1
  m_FlareFadeSpeed: 3
  m_HaloTexture: {fileID: 0}
  m_SpotCookie: {fileID: 10001, guid: 0000000000000000e000000000000000, type: 0}
  m_DefaultReflectionMode: 0
  m_DefaultReflectionResolution: 128
  m_ReflectionBounces: 1
  m_ReflectionIntensity: 1
  m_CustomReflection: {fileID: 0}
  m_Sun: {fileID: 0}
  m_UseRadianceAmbientProbe: 0
--- !u!157 &3
LightmapSettings:
  m_ObjectHideFlags: 0
  serializedVersion: 13
  m_BakeOnSceneLoad: 0
  m_GISettings:
    serializedVersion: 2
    m_BounceScale: 1
    m_IndirectOutputScale: 1
    m_AlbedoBoost: 1
    m_EnvironmentLightingMode: 0
    m_EnableBakedLightmaps: 1
    m_EnableRealtimeLightmaps: 0
  m_LightmapEditorSettings:
    serializedVersion: 12
    m_Resolution: 2
    m_BakeResolution: 40
    m_AtlasSize: 1024
    m_AO: 0
    m_AOMaxDistance: 1
    m_CompAOExponent: 1
    m_CompAOExponentDirect: 0
    m_ExtractAmbientOcclusion: 0
    m_Padding: 2
    m_LightmapParameters: {fileID: 0}
    m_LightmapsBakeMode: 1
    m_TextureCompression: 1
    m_ReflectionCompression: 2
    m_MixedBakeMode: 2
    m_BakeBackend: 1
    m_PVRSampling: 1
    m_PVRDirectSampleCount: 32
    m_PVRSampleCount: 512
    m_PVRBounces: 2
    m_PVREnvironmentSampleCount: 256
    m_PVREnvironmentReferencePointCount: 2048
    m_PVRFilteringMode: 1
    m_PVRDenoiserTypeDirect: 1
    m_PVRDenoiserTypeIndirect: 1
    m_PVRDenoiserTypeAO: 1
    m_PVRFilterTypeDirect: 0
    m_PVRFilterTypeIndirect: 0
    m_PVRFilterTypeAO: 0
    m_PVREnvironmentMIS: 1
    m_PVRCulling: 1
    m_PVRFilteringGaussRadiusDirect: 1
    m_PVRFilteringGaussRadiusIndirect: 5
    m_PVRFilteringGaussRadiusAO: 2
    m_PVRFilteringAtrousPositionSigmaDirect: 0.5
    m_PVRFilteringAtrousPositionSigmaIndirect: 2
    m_PVRFilteringAtrousPositionSigmaAO: 1
    m_ExportTrainingData: 0
    m_TrainingDataDestination: TrainingData
    m_LightProbeSampleCountMultiplier: 4
  m_LightingDataAsset: {fileID: 0}
  m_LightingSettings: {fileID: 0}
--- !u!196 &4
NavMeshSettings:
  serializedVersion: 2
  m_ObjectHideFlags: 0
  m_BuildSettings:
    serializedVersion: 3
    agentTypeID: 0
    agentRadius: 0.5
    agentHeight: 2
    agentSlope: 45
    agentClimb: 0.4
    ledgeDropHeight: 0
    maxJumpAcrossDistance: 0
    minRegionArea: 2
    manualCellSize: 0
    cellSize: 0.16666667
    manualTileSize: 0
    tileSize: 256
    buildHeightMesh: 0
    maxJobWorkers: 0
    preserveTilesOutsideBounds: 0
    debug:
      m_Flags: 0
  m_NavMeshData: {fileID: 0}
--- !u!1 &79434357 stripped
GameObject:
  m_CorrespondingSourceObject: {fileID: 8277932960352388903, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
  m_PrefabInstance: {fileID: 8014876639037405588}
  m_PrefabAsset: {fileID: 0}
--- !u!1 &101355896
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 101355900}
  - component: {fileID: 101355899}
  - component: {fileID: 101355898}
  - component: {fileID: 101355897}
  m_Layer: 0
  m_Name: Cube
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &101355897
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 101355896}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &101355898
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 101355896}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: 6968e89851431104b8f931c37d69356b, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &101355899
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 101355896}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &101355900
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 101355896}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.36192, y: 2.5, z: 5.58822}
  m_LocalScale: {x: 8, y: 5, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 468291111}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &105474242
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 105474246}
  - component: {fileID: 105474245}
  - component: {fileID: 105474244}
  - component: {fileID: 105474243}
  m_Layer: 0
  m_Name: Cube (2)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &105474243
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 105474242}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &105474244
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 105474242}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: a3cd22eceea5e8548b10cdb5d9b49a25, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &105474245
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 105474242}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &105474246
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 105474242}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.36192, y: 0.625, z: 16.05}
  m_LocalScale: {x: 2, y: 1.25, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 468291111}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &157207112
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 157207114}
  - component: {fileID: 157207113}
  - component: {fileID: 157207115}
  m_Layer: 0
  m_Name: Sun
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!108 &157207113
Light:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 157207112}
  m_Enabled: 1
  serializedVersion: 11
  m_Type: 1
  m_Color: {r: 1, g: 1, b: 1, a: 1}
  m_Intensity: 130000
  m_Range: 10
  m_SpotAngle: 30
  m_InnerSpotAngle: 21.80208
  m_CookieSize: 10
  m_Shadows:
    m_Type: 2
    m_Resolution: -1
    m_CustomResolution: -1
    m_Strength: 1
    m_Bias: 0.05
    m_NormalBias: 0.4
    m_NearPlane: 0.2
    m_CullingMatrixOverride:
      e00: 1
      e01: 0
      e02: 0
      e03: 0
      e10: 0
      e11: 1
      e12: 0
      e13: 0
      e20: 0
      e21: 0
      e22: 1
      e23: 0
      e30: 0
      e31: 0
      e32: 0
      e33: 1
    m_UseCullingMatrixOverride: 0
  m_Cookie: {fileID: 0}
  m_DrawHalo: 0
  m_Flare: {fileID: 0}
  m_RenderMode: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingLayerMask: 1
  m_Lightmapping: 4
  m_LightShadowCasterMode: 2
  m_AreaSize: {x: 0.5, y: 0.5}
  m_BounceIntensity: 1
  m_ColorTemperature: 6500
  m_UseColorTemperature: 1
  m_BoundingSphereOverride: {x: 0, y: 0, z: 0, w: 0}
  m_UseBoundingSphereOverride: 0
  m_UseViewFrustumForShadowCasterCull: 1
  m_ForceVisible: 0
  m_ShadowRadius: 0
  m_ShadowAngle: 2.5
  m_LightUnit: 2
  m_LuxAtDistance: 1
  m_EnableSpotReflector: 0
--- !u!4 &157207114
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 157207112}
  serializedVersion: 2
  m_LocalRotation: {x: 0.38268343, y: 0, z: 0, w: 0.92387956}
  m_LocalPosition: {x: 0, y: 3, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 45, y: 0, z: 0}
--- !u!114 &157207115
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 157207112}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 7a68c43fe1f2a47cfa234b5eeaa98012, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_PointlightHDType: 0
  m_SpotLightShape: 0
  m_AreaLightShape: 0
  m_EnableSpotReflector: 0
  m_LightUnit: 2
  m_LuxAtDistance: 1
  m_Intensity: 130000
  m_InnerSpotPercent: 0
  m_SpotIESCutoffPercent: 100
  m_LightDimmer: 1
  m_VolumetricDimmer: 1
  m_FadeDistance: 10000
  m_VolumetricFadeDistance: 10000
  m_AffectDiffuse: 1
  m_AffectSpecular: 1
  m_NonLightmappedOnly: 0
  m_ShapeWidth: 0.5
  m_ShapeHeight: 0.5
  m_AspectRatio: 1
  m_ShapeRadius: 0.025
  m_SoftnessScale: 1
  m_UseCustomSpotLightShadowCone: 0
  m_CustomSpotLightShadowCone: 30
  m_MaxSmoothness: 0.99
  m_ApplyRangeAttenuation: 1
  m_DisplayAreaLightEmissiveMesh: 0
  m_AreaLightCookie: {fileID: 0}
  m_IESPoint: {fileID: 0}
  m_IESSpot: {fileID: 0}
  m_IncludeForRayTracing: 1
  m_IncludeForPathTracing: 1
  m_AreaLightShadowCone: 120
  m_UseScreenSpaceShadows: 0
  m_InteractsWithSky: 1
  m_AngularDiameter: 2.5
  diameterMultiplerMode: 0
  diameterMultiplier: 1
  diameterOverride: 0.5
  celestialBodyShadingSource: 1
  sunLightOverride: {fileID: 0}
  sunColor: {r: 1, g: 1, b: 1, a: 1}
  sunIntensity: 130000
  moonPhase: 0.2
  moonPhaseRotation: 0
  earthshine: 1
  flareSize: 2
  flareTint: {r: 1, g: 1, b: 1, a: 1}
  flareFalloff: 4
  flareMultiplier: 1
  surfaceTexture: {fileID: 0}
  surfaceTint: {r: 1, g: 1, b: 1, a: 1}
  m_Distance: 1.5e+11
  m_UseRayTracedShadows: 0
  m_NumRayTracingSamples: 4
  m_FilterTracedShadow: 1
  m_FilterSizeTraced: 16
  m_SunLightConeAngle: 0.5
  m_LightShadowRadius: 0.5
  m_SemiTransparentShadow: 0
  m_ColorShadow: 1
  m_DistanceBasedFiltering: 0
  m_EvsmExponent: 15
  m_EvsmLightLeakBias: 0
  m_EvsmVarianceBias: 0.00001
  m_EvsmBlurPasses: 0
  m_LightlayersMask: 1
  m_LinkShadowLayers: 1
  m_ShadowNearPlane: 0.1
  m_BlockerSampleCount: 24
  m_FilterSampleCount: 16
  m_MinFilterSize: 0.1
  m_DirLightPCSSBlockerSampleCount: 24
  m_DirLightPCSSFilterSampleCount: 16
  m_DirLightPCSSMaxPenumbraSize: 0.56
  m_DirLightPCSSMaxSamplingDistance: 0.5
  m_DirLightPCSSMinFilterSizeTexels: 1.5
  m_DirLightPCSSMinFilterMaxAngularDiameter: 10
  m_DirLightPCSSBlockerSearchAngularDiameter: 12
  m_DirLightPCSSBlockerSamplingClumpExponent: 2
  m_KernelSize: 5
  m_LightAngle: 1
  m_MaxDepthBias: 0.001
  m_ShadowResolution:
    m_Override: 512
    m_UseOverride: 1
    m_Level: 0
  m_ShadowDimmer: 1
  m_VolumetricShadowDimmer: 1
  m_ShadowFadeDistance: 10000
  m_UseContactShadow:
    m_Override: 0
    m_UseOverride: 1
    m_Level: 0
  m_RayTracedContactShadow: 0
  m_ShadowTint: {r: 0, g: 0, b: 0, a: 1}
  m_PenumbraTint: 0
  m_NormalBias: 0.75
  m_SlopeBias: 0.5
  m_ShadowUpdateMode: 0
  m_AlwaysDrawDynamicShadows: 0
  m_UpdateShadowOnLightMovement: 0
  m_CachedShadowTranslationThreshold: 0.01
  m_CachedShadowAngularThreshold: 0.5
  m_BarnDoorAngle: 90
  m_BarnDoorLength: 0.05
  m_preserveCachedShadow: 0
  m_OnDemandShadowRenderOnPlacement: 1
  m_ShadowCascadeRatios:
  - 0.05
  - 0.2
  - 0.3
  m_ShadowCascadeBorders:
  - 0.2
  - 0.2
  - 0.2
  - 0.2
  m_ShadowAlgorithm: 0
  m_ShadowVariant: 0
  m_ShadowPrecision: 0
  useOldInspector: 0
  useVolumetric: 1
  featuresFoldout: 1
  m_AreaLightEmissiveMeshShadowCastingMode: 0
  m_AreaLightEmissiveMeshMotionVectorGenerationMode: 0
  m_AreaLightEmissiveMeshLayer: -1
  m_Version: 13
  m_ObsoleteShadowResolutionTier: 1
  m_ObsoleteUseShadowQualitySettings: 0
  m_ObsoleteCustomShadowResolution: 512
  m_ObsoleteContactShadows: 0
--- !u!4 &275898573 stripped
Transform:
  m_CorrespondingSourceObject: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
  m_PrefabInstance: {fileID: 8014876639037405588}
  m_PrefabAsset: {fileID: 0}
--- !u!143 &275898575 stripped
CharacterController:
  m_CorrespondingSourceObject: {fileID: 7275538768717245028, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
  m_PrefabInstance: {fileID: 8014876639037405588}
  m_PrefabAsset: {fileID: 0}
--- !u!114 &275898579
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 79434357}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: deb6aab23fd63524e8fd65b9dbc41c92, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::PlayerController
  controller: {fileID: 275898575}
  cameraRoot: {fileID: 504053553}
  cameraOrbit: {fileID: 743096351}
  walkSpeed: 5
  sprintSpeed: 8
  rotationLerp: 12
  jumpHeight: 1.2
  gravity: -20
  groundMask:
    serializedVersion: 2
    m_Bits: 4294967295
  mouseRayMaxDist: 500
  fallbackToPlane: 1
  tpsAlignToCameraYaw: 1
  bodyTurnSpeed: 540
  yawDeadZone: 8
  allowFreeLook: 1
  Settings: {fileID: 11400000, guid: 9c615b886460c484baf9609d4b613bf6, type: 2}
--- !u!1 &432432459
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 432432461}
  - component: {fileID: 432432460}
  m_Layer: 0
  m_Name: Sky and Fog Volume
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &432432460
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 432432459}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 172515602e62fb746b5d573b38a5fe58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_IsGlobal: 1
  priority: 0
  blendDistance: 0
  weight: 1
  sharedProfile: {fileID: 11400000, guid: 8ba92e2dd7f884a0f88b98fa2d235fe7, type: 2}
--- !u!4 &432432461
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 432432459}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &468291110
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 468291111}
  m_Layer: 0
  m_Name: Root
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &468291111
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 468291110}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children:
  - {fileID: 1804236806}
  - {fileID: 101355900}
  - {fileID: 1031786634}
  - {fileID: 105474246}
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &504053547
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 504053553}
  - component: {fileID: 504053552}
  - component: {fileID: 504053551}
  - component: {fileID: 504053550}
  - component: {fileID: 504053549}
  - component: {fileID: 504053548}
  m_Layer: 0
  m_Name: Camera
  m_TagString: MainCamera
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &504053548
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 504053547}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: cf9bc49ded1b5554f9f3ca1b9a103bae, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::CameraSwitcherCinemachine
  TopDownCam: {fileID: 1603037727}
  TpsCam: {fileID: 758240765}
  Settings: {fileID: 11400000, guid: 9c615b886460c484baf9609d4b613bf6, type: 2}
  player: {fileID: 275898579}
  Mode: 0
--- !u!114 &504053549
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 504053547}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 72ece51f2901e7445ab60da3685d6b5f, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.Cinemachine::Unity.Cinemachine.CinemachineBrain
  ShowDebugText: 0
  ShowCameraFrustum: 1
  IgnoreTimeScale: 0
  WorldUpOverride: {fileID: 0}
  ChannelMask: -1
  UpdateMethod: 2
  BlendUpdateMethod: 1
  LensModeOverride:
    Enabled: 0
    DefaultMode: 2
  DefaultBlend:
    Style: 1
    Time: 2
    CustomCurve:
      serializedVersion: 2
      m_Curve: []
      m_PreInfinity: 2
      m_PostInfinity: 2
      m_RotationOrder: 4
  CustomBlends: {fileID: 0}
--- !u!114 &504053550
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 504053547}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 23c1ce4fb46143f46bc5cb5224c934f6, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.RenderPipelines.HighDefinition.Runtime::UnityEngine.Rendering.HighDefinition.HDAdditionalCameraData
  clearColorMode: 0
  backgroundColorHDR: {r: 0.025, g: 0.07, b: 0.19, a: 0}
  clearDepth: 1
  volumeLayerMask:
    serializedVersion: 2
    m_Bits: 1
  volumeAnchorOverride: {fileID: 0}
  antialiasing: 0
  SMAAQuality: 2
  dithering: 0
  stopNaNs: 0
  taaSharpenStrength: 0.5
  TAAQuality: 1
  taaSharpenMode: 0
  taaRingingReduction: 0
  taaHistorySharpening: 0.35
  taaAntiFlicker: 0.5
  taaMotionVectorRejection: 0
  taaAntiHistoryRinging: 0
  taaBaseBlendFactor: 0.875
  taaJitterScale: 1
  physicalParameters:
    m_Iso: 200
    m_ShutterSpeed: 0.005
    m_Aperture: 16
    m_FocusDistance: 10
    m_BladeCount: 5
    m_Curvature: {x: 2, y: 11}
    m_BarrelClipping: 0.25
    m_Anamorphism: 0
  flipYMode: 0
  xrRendering: 1
  fullscreenPassthrough: 0
  allowDynamicResolution: 0
  customRenderingSettings: 0
  invertFaceCulling: 0
  probeLayerMask:
    serializedVersion: 2
    m_Bits: 4294967295
  hasPersistentHistory: 0
  screenSizeOverride: {x: 0, y: 0, z: 0, w: 0}
  screenCoordScaleBias: {x: 0, y: 0, z: 0, w: 0}
  allowDeepLearningSuperSampling: 1
  deepLearningSuperSamplingUseCustomQualitySettings: 0
  deepLearningSuperSamplingQuality: 0
  deepLearningSuperSamplingUseCustomAttributes: 0
  deepLearningSuperSamplingUseOptimalSettings: 1
  deepLearningSuperSamplingSharpening: 0
  allowFidelityFX2SuperResolution: 1
  fidelityFX2SuperResolutionUseCustomQualitySettings: 0
  fidelityFX2SuperResolutionQuality: 0
  fidelityFX2SuperResolutionUseCustomAttributes: 0
  fidelityFX2SuperResolutionUseOptimalSettings: 1
  fidelityFX2SuperResolutionEnableSharpening: 0
  fidelityFX2SuperResolutionSharpening: 0
  fsrOverrideSharpness: 0
  fsrSharpness: 0.92
  exposureTarget: {fileID: 0}
  materialMipBias: 0
  m_RenderingPathCustomFrameSettings:
    bitDatas:
      data1: 5770166122053581
      data2: 12934340311651418136
    lodBias: 1
    lodBiasMode: 0
    lodBiasQualityLevel: 0
    maximumLODLevel: 0
    maximumLODLevelMode: 0
    maximumLODLevelQualityLevel: 0
    sssQualityMode: 0
    sssQualityLevel: 0
    sssCustomSampleBudget: 20
    sssCustomDownsampleSteps: 0
    msaaMode: 1
    materialQuality: 0
  renderingPathCustomFrameSettingsOverrideMask:
    mask:
      data1: 0
      data2: 0
  defaultFrameSettings: 0
  m_Version: 9
  m_ObsoleteRenderingPath: 0
  m_ObsoleteFrameSettings:
    overrides: 0
    enableShadow: 0
    enableContactShadows: 0
    enableShadowMask: 0
    enableSSR: 0
    enableSSAO: 0
    enableSubsurfaceScattering: 0
    enableTransmission: 0
    enableAtmosphericScattering: 0
    enableVolumetrics: 0
    enableReprojectionForVolumetrics: 0
    enableLightLayers: 0
    enableExposureControl: 1
    diffuseGlobalDimmer: 0
    specularGlobalDimmer: 0
    shaderLitMode: 0
    enableDepthPrepassWithDeferredRendering: 0
    enableTransparentPrepass: 0
    enableMotionVectors: 0
    enableObjectMotionVectors: 0
    enableDecals: 0
    enableRoughRefraction: 0
    enableTransparentPostpass: 0
    enableDistortion: 0
    enablePostprocess: 0
    enableOpaqueObjects: 0
    enableTransparentObjects: 0
    enableRealtimePlanarReflection: 0
    enableMSAA: 0
    enableAsyncCompute: 0
    runLightListAsync: 0
    runSSRAsync: 0
    runSSAOAsync: 0
    runContactShadowsAsync: 0
    runVolumeVoxelizationAsync: 0
    lightLoopSettings:
      overrides: 0
      enableDeferredTileAndCluster: 0
      enableComputeLightEvaluation: 0
      enableComputeLightVariants: 0
      enableComputeMaterialVariants: 0
      enableFptlForForwardOpaque: 0
      enableBigTilePrepass: 0
      isFptlEnabled: 0
--- !u!81 &504053551
AudioListener:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 504053547}
  m_Enabled: 1
--- !u!20 &504053552
Camera:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 504053547}
  m_Enabled: 1
  serializedVersion: 2
  m_ClearFlags: 1
  m_BackGroundColor: {r: 0.19215687, g: 0.3019608, b: 0.4745098, a: 0}
  m_projectionMatrixMode: 1
  m_GateFitMode: 1
  m_FOVAxisMode: 0
  m_Iso: 200
  m_ShutterSpeed: 0.005
  m_Aperture: 16
  m_FocusDistance: 10
  m_FocalLength: 50
  m_BladeCount: 5
  m_Curvature: {x: 2, y: 11}
  m_BarrelClipping: 0.25
  m_Anamorphism: 0
  m_SensorSize: {x: 36, y: 24}
  m_LensShift: {x: 0, y: 0}
  m_NormalizedViewPortRect:
    serializedVersion: 2
    x: 0
    y: 0
    width: 1
    height: 1
  near clip plane: 0.1
  far clip plane: 5000
  field of view: 40
  orthographic: 0
  orthographic size: 10
  m_Depth: 0
  m_CullingMask:
    serializedVersion: 2
    m_Bits: 4294967295
  m_RenderingPath: -1
  m_TargetTexture: {fileID: 0}
  m_TargetDisplay: 0
  m_TargetEye: 3
  m_HDR: 0
  m_AllowMSAA: 0
  m_AllowDynamicResolution: 0
  m_ForceIntoRT: 0
  m_OcclusionCulling: 1
  m_StereoConvergence: 10
  m_StereoSeparation: 0.022
--- !u!4 &504053553
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 504053547}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0.08000002, y: 1.5, z: -2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &743096349
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 743096350}
  - component: {fileID: 743096351}
  m_Layer: 0
  m_Name: PlayerCameraRoot
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &743096350
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 743096349}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 1.5, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 275898573}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &743096351
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 743096349}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 830b9bb7846d02847b7cfbc0dc6df4d9, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::CameraTargetOrbit
  settings: {fileID: 11400000, guid: 9c615b886460c484baf9609d4b613bf6, type: 2}
  player: {fileID: 275898579}
  vcamTPS: {fileID: 758240765}
  pitchMin: -50
  pitchMax: 70
--- !u!1 &758240762
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 758240766}
  - component: {fileID: 758240765}
  - component: {fileID: 758240768}
  - component: {fileID: 758240767}
  m_Layer: 0
  m_Name: Cinemachine Camera TPS
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &758240765
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 758240762}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f9dfa5b682dcd46bda6128250e975f58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.Cinemachine::Unity.Cinemachine.CinemachineCamera
  Priority:
    Enabled: 1
    m_Value: 10
  OutputChannel: 1
  StandbyUpdate: 2
  m_StreamingVersion: 20241001
  m_LegacyPriority: 0
  Target:
    TrackingTarget: {fileID: 743096350}
    LookAtTarget: {fileID: 0}
    CustomLookAtTarget: 0
  Lens:
    FieldOfView: 40
    OrthographicSize: 10
    NearClipPlane: 0.1
    FarClipPlane: 5000
    Dutch: 0
    ModeOverride: 0
    PhysicalProperties:
      GateFit: 2
      SensorSize: {x: 21.946, y: 16.002}
      LensShift: {x: 0, y: 0}
      FocusDistance: 10
      Iso: 200
      ShutterSpeed: 0.005
      Aperture: 16
      BladeCount: 5
      Curvature: {x: 2, y: 11}
      BarrelClipping: 0.25
      Anamorphism: 0
  BlendHint: 8
--- !u!4 &758240766
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 758240762}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0.08000002, y: 1.5, z: -2}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!114 &758240767
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 758240762}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 68bb026fafb42b14791938953eaace77, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.Cinemachine::Unity.Cinemachine.CinemachineBasicMultiChannelPerlin
  NoiseProfile: {fileID: 11400000, guid: 46965f9cbaf525742a6da4c2172a99cd, type: 2}
  PivotOffset: {x: 0, y: 0, z: 0}
  AmplitudeGain: 0.5
  FrequencyGain: 0.3
  m_NoiseOffsets: {x: 0, y: 0, z: 0}
--- !u!114 &758240768
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 758240762}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 625c14eee3ad46c99df7c7c891ef668a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.Cinemachine::Unity.Cinemachine.CinemachineThirdPersonFollow
  Damping: {x: 0, y: 0, z: 0}
  ShoulderOffset: {x: 0.4, y: 0, z: 0}
  VerticalArmLength: 0
  CameraSide: 0.6
  CameraDistance: 2
  AvoidObstacles:
    Enabled: 1
    CollisionFilter:
      serializedVersion: 2
      m_Bits: 1
    IgnoreTag: Player
    CameraRadius: 0.15
    DampingIntoCollision: 0
    DampingFromCollision: 0
--- !u!1 &1031786630
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1031786634}
  - component: {fileID: 1031786633}
  - component: {fileID: 1031786632}
  - component: {fileID: 1031786631}
  m_Layer: 0
  m_Name: Cube (1)
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!65 &1031786631
BoxCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1031786630}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 3
  m_Size: {x: 1, y: 1, z: 1}
  m_Center: {x: 0, y: 0, z: 0}
--- !u!23 &1031786632
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1031786630}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: c70e075a2144ba04784d74b7b2889fd3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1031786633
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1031786630}
  m_Mesh: {fileID: 10202, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1031786634
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1031786630}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 1.36192, y: 1.25, z: 11.04}
  m_LocalScale: {x: 4, y: 2.5, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 468291111}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1131566560
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1131566561}
  m_Layer: 0
  m_Name: --- SCENES ---
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1131566561
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1131566560}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1259650104
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1259650105}
  m_Layer: 0
  m_Name: --- SCRIPTS ---
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1259650105
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1259650104}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1557847335
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1557847337}
  - component: {fileID: 1557847336}
  m_Layer: 0
  m_Name: InputManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1557847336
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1557847335}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 62899f850307741f2a39c98a8b639597, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.InputSystem::UnityEngine.InputSystem.PlayerInput
  m_Actions: {fileID: -944628639613478452, guid: 35845fe01580c41289b024647b1d1c53, type: 3}
  m_NotificationBehavior: 0
  m_UIInputModule: {fileID: 0}
  m_DeviceLostEvent:
    m_PersistentCalls:
      m_Calls: []
  m_DeviceRegainedEvent:
    m_PersistentCalls:
      m_Calls: []
  m_ControlsChangedEvent:
    m_PersistentCalls:
      m_Calls: []
  m_ActionEvents: []
  m_NeverAutoSwitchControlSchemes: 0
  m_DefaultControlScheme: Keyboard&Mouse
  m_DefaultActionMap: Player
  m_SplitScreenIndex: -1
  m_Camera: {fileID: 0}
--- !u!4 &1557847337
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1557847335}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1603037726
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1603037728}
  - component: {fileID: 1603037727}
  - component: {fileID: 1603037729}
  m_Layer: 0
  m_Name: Cinemachine Camera TopDown
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &1603037727
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603037726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: f9dfa5b682dcd46bda6128250e975f58, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.Cinemachine::Unity.Cinemachine.CinemachineCamera
  Priority:
    Enabled: 0
    m_Value: 0
  OutputChannel: 1
  StandbyUpdate: 2
  m_StreamingVersion: 20241001
  m_LegacyPriority: 0
  Target:
    TrackingTarget: {fileID: 275898573}
    LookAtTarget: {fileID: 0}
    CustomLookAtTarget: 0
  Lens:
    FieldOfView: 60.000004
    OrthographicSize: 10
    NearClipPlane: 0.1
    FarClipPlane: 5000
    Dutch: 0
    ModeOverride: 0
    PhysicalProperties:
      GateFit: 2
      SensorSize: {x: 21.946, y: 16.002}
      LensShift: {x: 0, y: 0}
      FocusDistance: 10
      Iso: 200
      ShutterSpeed: 0.005
      Aperture: 16
      BladeCount: 5
      Curvature: {x: 2, y: 11}
      BarrelClipping: 0.25
      Anamorphism: 0
  BlendHint: 0
--- !u!4 &1603037728
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603037726}
  serializedVersion: 2
  m_LocalRotation: {x: 0.5, y: 0, z: 0, w: 0.8660254}
  m_LocalPosition: {x: 0, y: 8, z: -5}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 60, y: 0, z: 0}
--- !u!114 &1603037729
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1603037726}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: b617507da6d07e749b7efdb34e1173e1, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Unity.Cinemachine::Unity.Cinemachine.CinemachineFollow
  TrackerSettings:
    BindingMode: 0
    PositionDamping: {x: 1, y: 1, z: 1}
    AngularDampingMode: 0
    RotationDamping: {x: 1, y: 1, z: 1}
    QuaternionDamping: 1
  FollowOffset: {x: 0, y: 8, z: -5}
--- !u!1 &1759435152
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1759435153}
  m_Layer: 0
  m_Name: --- LIGHT & CAMERAS ---
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!4 &1759435153
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1759435152}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &1804236802
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 1804236806}
  - component: {fileID: 1804236805}
  - component: {fileID: 1804236804}
  - component: {fileID: 1804236803}
  m_Layer: 0
  m_Name: Ground
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!64 &1804236803
MeshCollider:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804236802}
  m_Material: {fileID: 0}
  m_IncludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_ExcludeLayers:
    serializedVersion: 2
    m_Bits: 0
  m_LayerOverridePriority: 0
  m_IsTrigger: 0
  m_ProvidesContacts: 0
  m_Enabled: 1
  serializedVersion: 5
  m_Convex: 0
  m_CookingOptions: 30
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!23 &1804236804
MeshRenderer:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804236802}
  m_Enabled: 1
  m_CastShadows: 1
  m_ReceiveShadows: 1
  m_DynamicOccludee: 1
  m_StaticShadowCaster: 0
  m_MotionVectors: 1
  m_LightProbeUsage: 1
  m_ReflectionProbeUsage: 1
  m_RayTracingMode: 2
  m_RayTraceProcedural: 0
  m_RayTracingAccelStructBuildFlagsOverride: 0
  m_RayTracingAccelStructBuildFlags: 1
  m_SmallMeshCulling: 1
  m_ForceMeshLod: -1
  m_MeshLodSelectionBias: 0
  m_RenderingLayerMask: 1
  m_RendererPriority: 0
  m_Materials:
  - {fileID: 2100000, guid: b705b36c0a031c74bb375ee73b5ecce3, type: 2}
  m_StaticBatchInfo:
    firstSubMesh: 0
    subMeshCount: 0
  m_StaticBatchRoot: {fileID: 0}
  m_ProbeAnchor: {fileID: 0}
  m_LightProbeVolumeOverride: {fileID: 0}
  m_ScaleInLightmap: 1
  m_ReceiveGI: 1
  m_PreserveUVs: 0
  m_IgnoreNormalsForChartDetection: 0
  m_ImportantGI: 0
  m_StitchLightmapSeams: 1
  m_SelectedEditorRenderState: 3
  m_MinimumChartSize: 4
  m_AutoUVMaxDistance: 0.5
  m_AutoUVMaxAngle: 89
  m_LightmapParameters: {fileID: 0}
  m_GlobalIlluminationMeshLod: 0
  m_SortingLayerID: 0
  m_SortingLayer: 0
  m_SortingOrder: 0
  m_AdditionalVertexStreams: {fileID: 0}
--- !u!33 &1804236805
MeshFilter:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804236802}
  m_Mesh: {fileID: 10209, guid: 0000000000000000e000000000000000, type: 0}
--- !u!4 &1804236806
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 1804236802}
  serializedVersion: 2
  m_LocalRotation: {x: -0, y: -0, z: -0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 10, y: 10, z: 10}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 468291111}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2013410473
GameObject:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2013410475}
  - component: {fileID: 2013410474}
  m_Layer: 0
  m_Name: StaticLightingSky
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2013410474
MonoBehaviour:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2013410473}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: 441482e8936e35048a1dffac814e3ef8, type: 3}
  m_Name: 
  m_EditorClassIdentifier: 
  m_Profile: {fileID: 11400000, guid: 8ba92e2dd7f884a0f88b98fa2d235fe7, type: 2}
  m_StaticLightingSkyUniqueID: 4
  m_StaticLightingCloudsUniqueID: 0
  m_StaticLightingVolumetricClouds: 0
  bounces: 1
--- !u!4 &2013410475
Transform:
  m_ObjectHideFlags: 1
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2013410473}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: 0, y: 0, z: 0}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1 &2124320999
GameObject:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  serializedVersion: 6
  m_Component:
  - component: {fileID: 2124321001}
  - component: {fileID: 2124321000}
  m_Layer: 0
  m_Name: SettingsManager
  m_TagString: Untagged
  m_Icon: {fileID: 0}
  m_NavMeshLayer: 0
  m_StaticEditorFlags: 0
  m_IsActive: 1
--- !u!114 &2124321000
MonoBehaviour:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2124320999}
  m_Enabled: 1
  m_EditorHideFlags: 0
  m_Script: {fileID: 11500000, guid: c3e3ebac20d964f41bcc25c040953d4a, type: 3}
  m_Name: 
  m_EditorClassIdentifier: Assembly-CSharp::SettingsManager
  settings: {fileID: 11400000, guid: 9c615b886460c484baf9609d4b613bf6, type: 2}
  playerInput: {fileID: 1557847336}
  topDownCam: {fileID: 1603037727}
  tpsCam: {fileID: 758240765}
--- !u!4 &2124321001
Transform:
  m_ObjectHideFlags: 0
  m_CorrespondingSourceObject: {fileID: 0}
  m_PrefabInstance: {fileID: 0}
  m_PrefabAsset: {fileID: 0}
  m_GameObject: {fileID: 2124320999}
  serializedVersion: 2
  m_LocalRotation: {x: 0, y: 0, z: 0, w: 1}
  m_LocalPosition: {x: -0.07841, y: 1.12972, z: -0.49381}
  m_LocalScale: {x: 1, y: 1, z: 1}
  m_ConstrainProportionsScale: 0
  m_Children: []
  m_Father: {fileID: 0}
  m_LocalEulerAnglesHint: {x: 0, y: 0, z: 0}
--- !u!1001 &8014876639037405588
PrefabInstance:
  m_ObjectHideFlags: 0
  serializedVersion: 2
  m_Modification:
    serializedVersion: 3
    m_TransformParent: {fileID: 0}
    m_Modifications:
    - target: {fileID: 1803828528209008661, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: camSwitcher
      value: 
      objectReference: {fileID: 504053548}
    - target: {fileID: 1803828528209008661, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: defaultView
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 1803828528209008661, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: rotationSpeed
      value: 540
      objectReference: {fileID: 0}
    - target: {fileID: 1803828528209008661, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: cameraTransform
      value: 
      objectReference: {fileID: 504053553}
    - target: {fileID: 8277932960352388903, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_Name
      value: Player
      objectReference: {fileID: 0}
    - target: {fileID: 8277932960352388903, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_TagString
      value: Player
      objectReference: {fileID: 0}
    - target: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_LocalPosition.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_LocalPosition.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_LocalPosition.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_LocalRotation.w
      value: 1
      objectReference: {fileID: 0}
    - target: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_LocalRotation.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_LocalRotation.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_LocalRotation.z
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.x
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.y
      value: 0
      objectReference: {fileID: 0}
    - target: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      propertyPath: m_LocalEulerAnglesHint.z
      value: 0
      objectReference: {fileID: 0}
    m_RemovedComponents:
    - {fileID: 1803828528209008661, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
    m_RemovedGameObjects:
    - {fileID: 1462434110190939828, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
    m_AddedGameObjects:
    - targetCorrespondingSourceObject: {fileID: 8336784990817851452, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      insertIndex: -1
      addedObject: {fileID: 743096350}
    m_AddedComponents:
    - targetCorrespondingSourceObject: {fileID: 8277932960352388903, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
      insertIndex: -1
      addedObject: {fileID: 275898579}
  m_SourcePrefab: {fileID: 100100000, guid: c80e6df8cf8199f41922ffe0aba18cb0, type: 3}
--- !u!1660057539 &9223372036854775807
SceneRoots:
  m_ObjectHideFlags: 0
  m_Roots:
  - {fileID: 2013410475}
  - {fileID: 1259650105}
  - {fileID: 2124321001}
  - {fileID: 1557847337}
  - {fileID: 1759435153}
  - {fileID: 157207114}
  - {fileID: 432432461}
  - {fileID: 1603037728}
  - {fileID: 758240766}
  - {fileID: 504053553}
  - {fileID: 1131566561}
  - {fileID: 8014876639037405588}
  - {fileID: 468291111}
