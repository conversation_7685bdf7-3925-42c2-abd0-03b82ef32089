using UnityEngine;
using UnityEngine.InputSystem;

[RequireComponent(typeof(CharacterController))]
public class MinimalPlayerController : MonoBehaviour
{
    public enum Mode { TopDown, TPS }
    public Mode mode = Mode.TPS;

    [Header("TPS options")]
    public bool tpsSyncYawWithCamera = false; // <-- OFF = orbite pure (perso ne tourne pas)


    [Header("Refs")]
    public CharacterController cc;
    public Transform cam;           // = Main Camera (avec Cinemachine Brain)
    public Transform rotateTarget;  // = l’enfant "X Bot" (Animator)

    [Header("Input")]
    public InputActionReference move;  // Vector2 (WASD en 2D Vector)

    [Header("Tuning")]
    public float speed = 6f;
    public float rotSpeed = 720f;   // deg/s
    public float gravity = -20f;

    private Vector3 vel;

    void Reset() { cc = GetComponent<CharacterController>(); }

    void Awake()
    {
        if (!cc) cc = GetComponent<CharacterController>();
        if (!cam && Camera.main) cam = Camera.main.transform;
        if (!rotateTarget) rotateTarget = transform; // fallback
    }

    void OnEnable() { move?.action.Enable(); }
    void OnDisable() { move?.action.Disable(); }

    void Update()
    {
        Vector2 m = move ? move.action.ReadValue<Vector2>() : Vector2.zero;
        Vector3 input = new Vector3(m.x, 0f, m.y);

        Vector3 dir;
        if (mode == Mode.TPS)
        {
            Vector3 f = Vector3.ProjectOnPlane(cam.forward, Vector3.up).normalized;
            Vector3 r = Vector3.ProjectOnPlane(cam.right, Vector3.up).normalized;
            dir = f * input.z + r * input.x;
        }
        else
        {
            dir = input; // world-space
        }
        if (dir.sqrMagnitude > 1f) dir.Normalize();

        Vector3 horiz = dir * speed;

        if (cc.isGrounded && vel.y < 0f) vel.y = -2f;
        vel.y += gravity * Time.deltaTime;

        cc.Move((horiz + vel) * Time.deltaTime);
    }

    void LateUpdate()
    {
        if (cam == null || rotateTarget == null) return;

        if (mode == Mode.TPS)
        {
            if (tpsSyncYawWithCamera)
            {
                Vector3 camF = Vector3.ProjectOnPlane(cam.forward, Vector3.up).normalized;
                if (camF.sqrMagnitude > 1e-4f)
                {
                    Quaternion target = Quaternion.LookRotation(camF, Vector3.up);
                    rotateTarget.rotation = Quaternion.RotateTowards(rotateTarget.rotation, target, rotSpeed * Time.deltaTime);
                }
            }
            else
            {
                // TopDown : vise le curseur sur un plan horizontal
                Ray ray = Camera.main.ScreenPointToRay(Mouse.current.position.ReadValue());
                Plane plane = new Plane(Vector3.up, new Vector3(0f, transform.position.y, 0f));
                if (plane.Raycast(ray, out float t))
                {
                    Vector3 p = ray.GetPoint(t);
                    Vector3 to = p - rotateTarget.position; to.y = 0f;
                    if (to.sqrMagnitude > 1e-4f)
                    {
                        Quaternion q = Quaternion.LookRotation(to, Vector3.up);
                        rotateTarget.rotation = Quaternion.RotateTowards(rotateTarget.rotation, q, rotSpeed * Time.deltaTime);
                    }
                }
            }
        }
    }
}
