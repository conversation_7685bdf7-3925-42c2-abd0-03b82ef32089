using UnityEngine;
using UnityEngine.InputSystem;
using Unity.Cinemachine;

public class SettingsManager : MonoBehaviour
{
    public static SettingsManager Instance { get; private set; }

    [Header("References")]
    public GameSettings settings;
    public PlayerInput playerInput;

    [Header("Optional vCams for initial FOV apply")]
    public CinemachineCamera topDownCam;
    public CinemachineCamera tpsCam;

    void Awake()
    {
        if (Instance != null && Instance != this) { Destroy(gameObject); return; }
        Instance = this;
        DontDestroyOnLoad(gameObject);

        // 1) Charger les réglages persistés
        settings?.Load();

        // auto-find
        if (!playerInput)
            playerInput = FindFirstObjectByType<PlayerInput>();

        // 2) Appliquer les overrides de bindings SUR L’INSTANCE RUNTIME
        // (doc: SaveBindingOverridesAsJson / LoadBindingOverridesFromJson)
        if (settings != null && playerInput != null)
            settings.ApplyRebindsTo(playerInput.actions); // méthode ajoutée dans GameSettings (cf. plus bas)

        // 3) Appliquer FOV initial si des vCams sont renseignées ici
        if (settings != null)
        {
            if (topDownCam)
            {
                var lens = topDownCam.Lens;        // LensSettings est un struct
                lens.FieldOfView = settings.fovTopDown;
                topDownCam.Lens = lens;            // on ré-assigne le struct modifié
            }
            if (tpsCam)
            {
                var lens = tpsCam.Lens;
                lens.FieldOfView = settings.fovTPS;
                tpsCam.Lens = lens;
            }
        }
    }

    // Accès pratique à une action par nom depuis n’importe où
    public InputAction GetAction(string actionName)
        => playerInput ? playerInput.actions.FindAction(actionName, throwIfNotFound: false) : null;
}
